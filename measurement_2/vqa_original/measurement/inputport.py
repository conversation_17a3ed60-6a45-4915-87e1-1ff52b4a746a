import copy
import numpy as np

from tools.tools_depr import compute_generator_expectation_values
from qiskit.quantum_info import DensityMatrix
from qiskit_aer import Aer
from qiskit import transpile
from qiskit.quantum_info.states import partial_trace
from qiskit import QuantumRegister, ClassicalRegister, QuantumCircuit
from IO import get_logger

logger = get_logger(__name__)
SIMULATOR = Aer.get_backend("aer_simulator")


def measure_all(qc, shots):
    global SIMULATOR
    qc.measure_all()
    t_circ = transpile(qc, SIMULATOR)
    result = SIMULATOR.run(t_circ, shots=shots).result()
    counts = result.get_counts(t_circ)
    return counts
