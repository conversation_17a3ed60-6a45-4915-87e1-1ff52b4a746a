#!/usr/bin/env python3
"""
测试VQA代码的基本导入功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, '.')

def test_basic_imports():
    """测试基本的Python包导入"""
    print("=== 测试基本Python包导入 ===")
    
    try:
        import numpy as np
        print("✓ NumPy导入成功")
    except ImportError as e:
        print(f"✗ NumPy导入失败: {e}")
        return False
    
    try:
        import scipy
        print("✓ SciPy导入成功")
    except ImportError as e:
        print(f"✗ SciPy导入失败: {e}")
        return False
    
    try:
        import qiskit
        print(f"✓ Qiskit导入成功, 版本: {qiskit.__version__}")
    except ImportError as e:
        print(f"✗ Qiskit导入失败: {e}")
        return False
    
    try:
        from qiskit_aer import Aer
        print("✓ Qiskit Aer导入成功")
    except ImportError as e:
        print(f"✗ Qiskit Aer导入失败: {e}")
        return False
    
    return True

def test_vqa_modules():
    """测试VQA模块导入"""
    print("\n=== 测试VQA模块导入 ===")
    
    # 测试IO模块
    try:
        from IO.logger import get_logger
        print("✓ IO.logger导入成功")
    except ImportError as e:
        print(f"✗ IO.logger导入失败: {e}")
    
    try:
        from IO.hdf5handler import HDF5Handler
        print("✓ IO.hdf5handler导入成功")
    except ImportError as e:
        print(f"✗ IO.hdf5handler导入失败: {e}")
    
    # 测试QM模块
    try:
        from QM.statevector import Statevector, Potential
        print("✓ QM.statevector导入成功")
    except ImportError as e:
        print(f"✗ QM.statevector导入失败: {e}")
    
    # 测试ansatz模块
    try:
        from ansatz import SU2
        print("✓ ansatz导入成功")
    except ImportError as e:
        print(f"✗ ansatz导入失败: {e}")
    
    # 测试measurement模块
    try:
        from measurement.measurehandler import MeasureHandler
        print("✓ measurement.measurehandler导入成功")
    except ImportError as e:
        print(f"✗ measurement.measurehandler导入失败: {e}")
    
    # 测试networks模块
    try:
        from networks import PotentialNetwork
        print("✓ networks导入成功")
    except ImportError as e:
        print(f"✗ networks导入失败: {e}")

def test_simple_circuit():
    """测试简单的量子电路创建"""
    print("\n=== 测试简单量子电路创建 ===")
    
    try:
        from qiskit import QuantumCircuit, QuantumRegister
        from qiskit_aer import Aer
        
        # 创建简单的量子电路
        qreg = QuantumRegister(2, 'q')
        qc = QuantumCircuit(qreg)
        qc.h(0)
        qc.cx(0, 1)
        
        # 获取模拟器
        simulator = Aer.get_backend('aer_simulator')
        
        print("✓ 简单量子电路创建成功")
        print(f"  电路量子比特数: {qc.num_qubits}")
        print(f"  模拟器: {simulator.name}")
        
        return True
        
    except Exception as e:
        print(f"✗ 量子电路创建失败: {e}")
        return False

if __name__ == "__main__":
    print("VQA代码导入测试")
    print("=" * 50)
    
    # 测试基本导入
    basic_ok = test_basic_imports()
    
    if basic_ok:
        # 测试VQA模块
        test_vqa_modules()
        
        # 测试量子电路
        test_simple_circuit()
    else:
        print("\n基本导入失败，请先安装必要的依赖包")
    
    print("\n测试完成!")
