#!/usr/bin/env python3
"""
VQA代码演示脚本
展示变分量子算法的基本功能
"""

import sys
import os
import numpy as np

# 添加当前目录到Python路径
sys.path.insert(0, '.')

def demo_ansatz():
    """演示ansatz功能"""
    print("=== Ansatz演示 ===")
    
    from ansatz import SU2
    
    # 创建不同配置的SU2 ansatz
    print("创建SU2 ansatz...")
    
    # 基本配置
    ansatz1 = SU2(ip_number=2, reps=1, layer_scheme="YC")
    print(f"✓ 2量子比特，1层重复，YC方案: {ansatz1.num_parameters}个参数")
    
    # 更复杂的配置
    ansatz2 = SU2(ip_number=3, reps=2, layer_scheme="YZC")
    print(f"✓ 3量子比特，2层重复，YZC方案: {ansatz2.num_parameters}个参数")
    
    # 显示量子电路信息
    print(f"✓ ansatz1电路深度: {ansatz1.qc.depth()}")
    print(f"✓ ansatz2电路深度: {ansatz2.qc.depth()}")
    
    return ansatz1, ansatz2

def demo_statevector():
    """演示状态向量功能"""
    print("\n=== 状态向量演示 ===")
    
    from QM.statevector import Statevector, Potential
    
    # 创建不同类型的状态向量
    print("创建状态向量...")
    
    # 平面波
    sv_plane = Statevector.init_plainwave(ip_number=2, k=np.pi)
    print(f"✓ 平面波状态: {len(sv_plane.get_amplitudes())}维")
    
    # 常数波函数
    sv_const = Statevector.init_const(ip_number=2)
    print(f"✓ 常数波函数: {len(sv_const.get_amplitudes())}维")
    
    # 谐振子基态
    sv_ho = Statevector.ho_groundstate(ip_number=2, omega=1.0)
    print(f"✓ 谐振子基态: {len(sv_ho.get_amplitudes())}维")
    
    # 创建势能
    print("\n创建势能...")
    potential_dict = {"HARMONIC": {"OMEGA": 1.0}}
    potential = Potential(num_ip=2, potential_dict=potential_dict)
    print(f"✓ 谐振子势能创建成功，类型: {potential.kind}")
    
    return sv_plane, sv_const, sv_ho, potential

def demo_quantum_circuit():
    """演示量子电路功能"""
    print("\n=== 量子电路演示 ===")
    
    from qiskit import QuantumCircuit, QuantumRegister
    from qiskit_aer import Aer
    from qiskit import transpile
    from ansatz import SU2
    
    # 创建参数化的量子电路
    print("创建参数化量子电路...")
    
    ansatz = SU2(ip_number=2, reps=1, layer_scheme="YC")
    
    # 绑定参数
    params = np.random.random(ansatz.num_parameters) * 2 * np.pi
    param_dict = dict(zip(ansatz.param_vector, params))
    bound_circuit = ansatz.qc.assign_parameters(param_dict)
    
    print(f"✓ 参数化电路创建成功，参数: {params}")
    print(f"✓ 绑定参数后的电路深度: {bound_circuit.depth()}")
    
    # 运行电路
    bound_circuit.save_statevector()
    simulator = Aer.get_backend('aer_simulator')
    transpiled_qc = transpile(bound_circuit, simulator)
    result = simulator.run(transpiled_qc).result()
    statevector = result.get_statevector()
    
    print(f"✓ 电路运行成功，最终状态向量: {np.round(statevector, 3)}")
    
    return bound_circuit, statevector

def demo_measurement():
    """演示测量功能"""
    print("\n=== 测量功能演示 ===")
    
    from measurement.ancilla import measure_sigma_exact, measure_sigma_plain
    from qiskit import QuantumCircuit, QuantumRegister
    from ansatz import SU2
    
    # 创建测试电路
    print("创建测试电路...")
    
    ansatz = SU2(ip_number=2, reps=1, layer_scheme="YC")
    params = np.array([np.pi/4, np.pi/3, np.pi/6, np.pi/2])
    param_dict = dict(zip(ansatz.param_vector, params))
    bound_circuit = ansatz.qc.assign_parameters(param_dict)
    
    # 精确测量
    print("进行精确测量...")
    sigma_exact, rho, purity = measure_sigma_exact(bound_circuit)
    print(f"✓ 精确测量结果: sigma={sigma_exact:.4f}, purity={purity:.4f}")
    
    # 采样测量
    print("进行采样测量...")
    sigma_sampled = measure_sigma_plain(bound_circuit.copy(), shots=1000)
    print(f"✓ 采样测量结果 (1000 shots): sigma={sigma_sampled:.4f}")
    
    return sigma_exact, sigma_sampled

def demo_networks():
    """演示网络功能"""
    print("\n=== 网络功能演示 ===")
    
    try:
        from networks import PotentialNetwork
        from ansatz import SU2
        
        print("创建势能网络...")
        
        # 创建势能网络
        potential_dict = {"HARMONIC": {"OMEGA": 1.0}}
        network = PotentialNetwork.construct(
            num_ip=2,
            hadamard=False,
            ansatz=None,
            POTENTIAL=potential_dict
        )
        
        print(f"✓ 势能网络创建成功")
        print(f"✓ 网络类型: {network.type}")
        print(f"✓ 网络电路量子比特数: {network.qc_assigned.num_qubits}")
        
        return network
        
    except Exception as e:
        print(f"⚠️  网络演示跳过: {e}")
        return None

def main():
    """主演示函数"""
    print("VQA (变分量子算法) 代码演示")
    print("=" * 60)
    
    try:
        # 演示各个模块
        ansatz1, ansatz2 = demo_ansatz()
        sv_plane, sv_const, sv_ho, potential = demo_statevector()
        bound_circuit, statevector = demo_quantum_circuit()
        sigma_exact, sigma_sampled = demo_measurement()
        network = demo_networks()
        
        print("\n" + "=" * 60)
        print("🎉 VQA代码演示完成！")
        print("\n主要功能验证:")
        print("✓ Ansatz (变分量子电路) 创建和参数化")
        print("✓ 状态向量和势能函数构建")
        print("✓ 量子电路运行和状态向量获取")
        print("✓ 精确和采样测量")
        if network:
            print("✓ 量子网络构建")
        
        print("\n这个VQA代码库可以用于:")
        print("• 变分量子本征求解器 (VQE)")
        print("• 量子化学计算")
        print("• 量子优化问题")
        print("• 量子机器学习")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
