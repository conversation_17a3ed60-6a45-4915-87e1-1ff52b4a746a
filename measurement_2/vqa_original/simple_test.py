#!/usr/bin/env python3
"""
简单的VQA代码测试，避免有问题的模块
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, '.')

def test_basic_vqa():
    """测试基本的VQA功能"""
    print("=== 测试基本VQA功能 ===")
    
    try:
        # 测试基本导入
        from qiskit import QuantumCircuit, QuantumRegister
        from qiskit_aer import Aer
        print("✓ Qiskit基本导入成功")
        
        # 测试ansatz
        from ansatz import SU2
        print("✓ ansatz模块导入成功")
        
        # 创建一个简单的SU2 ansatz
        su2_ansatz = SU2(ip_number=2, reps=1, layer_scheme="YC")
        print(f"✓ SU2 ansatz创建成功，参数数量: {su2_ansatz.num_parameters}")
        
        # 测试QM模块
        from QM.statevector import Statevector, Potential
        print("✓ QM模块导入成功")
        
        # 创建一个简单的状态向量
        import numpy as np
        amplitudes = np.array([1, 0, 0, 0]) / np.sqrt(1)
        x_coords = np.array([0, 1, 2, 3])
        sv = Statevector(amplitudes, x_coords)
        print(f"✓ Statevector创建成功，维度: {len(sv.get_amplitudes())}")
        
        # 测试势能
        potential_dict = {"HARMONIC": {"OMEGA": 1.0}}
        potential = Potential(num_ip=2, potential_dict=potential_dict)
        print("✓ Potential创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quantum_circuit():
    """测试量子电路功能"""
    print("\n=== 测试量子电路功能 ===")
    
    try:
        from qiskit import QuantumCircuit, QuantumRegister
        from qiskit_aer import Aer
        from qiskit import transpile
        import numpy as np
        
        # 创建简单的量子电路
        qreg = QuantumRegister(2, 'q')
        qc = QuantumCircuit(qreg)
        qc.h(0)
        qc.cx(0, 1)
        
        # 添加状态向量保存
        qc.save_statevector()
        
        # 获取模拟器并运行
        simulator = Aer.get_backend('aer_simulator')
        transpiled_qc = transpile(qc, simulator)
        result = simulator.run(transpiled_qc).result()
        statevector = result.get_statevector()
        
        print("✓ 量子电路创建和运行成功")
        print(f"  电路量子比特数: {qc.num_qubits}")
        print(f"  状态向量维度: {len(statevector)}")
        print(f"  状态向量: {np.round(statevector, 3)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 量子电路测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_measurement():
    """测试测量功能"""
    print("\n=== 测试测量功能 ===")
    
    try:
        # 测试measurement模块的基本导入
        from measurement.ancilla import measure_sigma_exact
        print("✓ measurement.ancilla导入成功")
        
        # 创建一个简单的量子电路进行测量
        from qiskit import QuantumCircuit, QuantumRegister
        qreg = QuantumRegister(2, 'q')
        qc = QuantumCircuit(qreg)
        qc.h(0)
        qc.cx(0, 1)
        
        # 测试精确测量
        sigma, rho, purity = measure_sigma_exact(qc)
        print(f"✓ 精确测量成功: sigma={sigma:.3f}, purity={purity:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测量测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("VQA代码简单测试")
    print("=" * 50)
    
    # 测试基本VQA功能
    basic_ok = test_basic_vqa()
    
    if basic_ok:
        # 测试量子电路
        circuit_ok = test_quantum_circuit()
        
        if circuit_ok:
            # 测试测量
            measurement_ok = test_measurement()
            
            if measurement_ok:
                print("\n🎉 所有基本测试通过！")
            else:
                print("\n⚠️  测量测试失败")
        else:
            print("\n⚠️  量子电路测试失败")
    else:
        print("\n❌ 基本测试失败")
    
    print("\n测试完成!")
