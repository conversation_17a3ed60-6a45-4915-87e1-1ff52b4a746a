import numpy as np
import scipy

from math import factorial


def binomial_coefficient(k, s):
    c_sk = factorial(k) / (factorial(s) * factorial(k - s))
    return c_sk


def phi(s, p, x_1_star, a_ks):
    val = 0
    for k in range(s, p + 1):
        if k != s:
            val += a_ks[k] * binomial_coefficient(k, s) * x_1_star ** (k - s)
        else:
            val += a_ks[k] * binomial_coefficient(k, s)
    return val


def G(x_m, n, m, p, x_0, stepsize, a_ks=None):
    # See paper: Constructive Representation of Functions in Low-RankTensor Formats I.V. Oseledets
    x_m_star = x_m * 2 ** (n - m) * stepsize  # 2 / (2 ** n)
    if m == 1:
        G = np.zeros((1, p + 1), dtype=float)
        for s in range(p + 1):
            x_1_star = x_m_star + x_0  # 2 ** (n - 1)
            phi_s = phi(s, p, x_1_star, a_ks)
            G[0, s] = phi_s

    elif n > m:
        G = np.zeros((p + 1, p + 1), dtype=float)
        for j in range(p + 1):
            for i in range(j, p + 1):
                if i != j:
                    G[i, j] = binomial_coefficient(i, i - j) * x_m_star ** (
                        i - j
                    )  # k is lower index, s upper index
                else:
                    G[i, i] = binomial_coefficient(i, i - j)
    elif n == m:
        G = np.zeros((p + 1, 1), dtype=float)
        for i in range(0, p + 1):
            if i != 0:
                G[i, 0] = x_m_star**i
            else:
                G[i, 0] = 1
    return G


def normalize_polynomial_by_coefficients(n, a_ks, x_0, x_n):
    if isinstance(a_ks, np.ndarray):
        if a_ks.size != 0:
            if a_ks.shape[0] == a_ks.size:
                pass
            else:
                raise ValueError("Coefficient array has wrong dims")
        else:
            raise ValueError("Empty coefficient array")

    elif isinstance(a_ks, list):
        if a_ks:
            a_ks = np.asarray(a_ks)
        else:
            raise ValueError("Empty coefficient array")
    else:
        raise ValueError("Coefficient array is neither np.ndarray, nor list")

    x_coordinates, stepsize = np.linspace(
        x_0, x_n, num=2**n, endpoint=False, retstep=True
    )
    amplitudes = []
    for x in x_coordinates:
        val = a_ks[0]
        for k, a_k in enumerate(a_ks[1:], start=1):
            val += a_k * x**k
        amplitudes.append(val)
    normalization_factor = scipy.linalg.norm(amplitudes)
    return a_ks / normalization_factor, normalization_factor, stepsize


def build_mps_polynomial(n=4, a_ks=[0, 0, 1], x_0=-1, x_n=1):
    p = len(a_ks) - 1
    MPS = []
    a_ks, alpha, stepsize = normalize_polynomial_by_coefficients(
        n, a_ks, x_0=x_0, x_n=x_n
    )
    # print(a_ks)
    for m in range(1, n + 1):
        A_m = []
        for x_m in [0, 1]:
            if m == 1:
                g_mat = G(x_m, n, m, p, x_0, stepsize, a_ks)
            else:
                g_mat = G(x_m, n, m, p, x_0, stepsize)
            A_m.append(g_mat)
        A_m = np.asarray(A_m)
        MPS.append(A_m)
    return MPS, alpha
