import numpy as np

from mpslibrary.operations import (
    contractMPS,
    qr_decompose,
    pad_mps,
    contract_start_tensors,
)
from mpslibrary.ttlowdim import build_mps_polynomial
from qiskit import QuantumCircuit, QuantumRegister
from mpslibrary.mpdunitaries import build_mpd_unitaries_from_mps


def apply_mpd_unitaries(n, s, unitaries):
    qreg = QuantumRegister(n)
    qc = QuantumCircuit(qreg)
    if s == 1:
        for j, unitary in enumerate(unitaries.__reversed__()):
            if j != (len(unitaries) - 1):
                qc.append(unitary, [j + 1, j])
            else:
                qc.append(unitary, [j])
    elif s == 2:
        for j, unitary in enumerate(unitaries.__reversed__()):
            if j != (len(unitaries) - 1):
                qc.append(unitary, [j + 2, j + 1, j])
            else:
                qc.append(unitary, [j + 1, j])

    elif s == 3:
        for j, unitary in enumerate(unitaries.__reversed__()):
            if j != (len(unitaries) - 1):
                qc.append(unitary, [j + 3, j + 2, j + 1, j])
            else:
                qc.append(unitary, [j + 2, j + 1, j])

    return qc


def construct_mpd_circuit(n, a_ks, x_0=-1, x_n=1):
    # Determine s needed to capture required bond dimension
    s = int(np.ceil(np.log2(len(a_ks))))
    chi = 2**s

    # Build the MPS of the polynomial function, pad it to chi,
    # and do a QR decomposition. Dependent on chi, contract first
    # tensors, so dimension will match for building unitaries
    MPS, alpha = build_mps_polynomial(n, a_ks=a_ks, x_0=x_0, x_n=x_n)
    pad_mps(MPS, chi)
    MPS = qr_decompose(MPS)
    results = contractMPS(MPS)
    MPS = contract_start_tensors(MPS, s=s)

    # Having the MPS by hand, building the MPD unitaries to
    # encode this state on the qubits
    unitaries = build_mpd_unitaries_from_mps(MPS, chi)
    qc = apply_mpd_unitaries(n=n, s=s, unitaries=unitaries)
    return qc, alpha, results


# construct_mpd_circuit(4, [0, 0, 1], x_0=-1, x_n=1)
