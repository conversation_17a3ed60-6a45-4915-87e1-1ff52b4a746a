import numpy as np

# 简化版本，专注于理论计算
simulator_available = False

# 专注于核心算法，不使用可视化

try:
    from scipy.optimize import minimize
    from scipy.linalg import svd
    scipy_available = True
except ImportError:
    scipy_available = False
    print("警告：scipy不可用，将使用numpy替代")

import warnings
warnings.filterwarnings('ignore')

class QuantumStateTomography:
    """
    单量子比特量子态层析实验类
    实现40组随机动态POVM测量方案
    """
    
    def __init__(self, target_bloch_vector=None):
        """
        初始化量子态层析实验
        
        Args:
            target_bloch_vector: 目标布洛赫向量 [rx, ry, rz]
                               默认为 [0.36, 0.48, 0.8]
        """
        if target_bloch_vector is None:
            self.target_bloch_vector = np.array([0.36, 0.48, 0.8])
        else:
            self.target_bloch_vector = np.array(target_bloch_vector)
        
        # 验证布洛赫向量长度
        bloch_length = np.linalg.norm(self.target_bloch_vector)
        if bloch_length > 1.0 + 1e-10:
            raise ValueError(f"布洛赫向量长度 {bloch_length} 超过1，不是有效的量子态")
        
        # 构造目标密度矩阵
        self.target_density_matrix = self._construct_density_matrix(self.target_bloch_vector)
        
        # 泡利矩阵
        self.pauli_matrices = {
            'I': np.array([[1, 0], [0, 1]], dtype=complex),
            'X': np.array([[0, 1], [1, 0]], dtype=complex),
            'Y': np.array([[0, -1j], [1j, 0]], dtype=complex),
            'Z': np.array([[1, 0], [0, -1]], dtype=complex)
        }
        
        # 存储40组POVM测量基
        self.povm_groups = []
        self.measurement_operators = []
        
        # 专注于理论计算，不使用量子模拟器
        
        print(f"目标布洛赫向量: {self.target_bloch_vector}")
        print(f"布洛赫向量长度: {bloch_length:.6f}")
        print("目标密度矩阵:")
        print(self.target_density_matrix)
    
    def _construct_density_matrix(self, bloch_vector):
        """
        根据布洛赫向量构造密度矩阵
        ρ = 1/2 * (I + r⃗ · σ⃗)
        """
        rx, ry, rz = bloch_vector
        I = self.pauli_matrices['I'] if hasattr(self, 'pauli_matrices') else np.eye(2, dtype=complex)
        sigma_x = np.array([[0, 1], [1, 0]], dtype=complex)
        sigma_y = np.array([[0, -1j], [1j, 0]], dtype=complex)
        sigma_z = np.array([[1, 0], [0, -1]], dtype=complex)
        
        rho = 0.5 * (I + rx * sigma_x + ry * sigma_y + rz * sigma_z)
        return rho
    
    def _generate_random_bloch_vector(self):
        """
        使用Haar测度生成随机布洛赫向量
        """
        # 使用Haar测度采样
        u = np.random.uniform(0, 1)
        v = np.random.uniform(0, 1)
        
        # 极角和方位角
        theta = np.arccos(2 * u - 1)  # [0, π]
        phi = 2 * np.pi * v          # [0, 2π]
        
        # 布洛赫向量
        nx = np.sin(theta) * np.cos(phi)
        ny = np.sin(theta) * np.sin(phi)
        nz = np.cos(theta)
        
        return np.array([nx, ny, nz])
    
    def _check_angle_regularity(self, vectors):
        """
        检查向量间夹角是否过于规律
        拒绝四面体角度附近的配置
        优化参数以提高随机性质量
        """
        tetrahedral_angle = np.arccos(-1/3)  # ≈ 109.47°
        tolerance = np.radians(3)  # 更严格的±3°容忍度

        angles = []
        for i in range(len(vectors)):
            for j in range(i+1, len(vectors)):
                cos_angle = np.clip(np.dot(vectors[i], vectors[j]), -1, 1)
                angle = np.arccos(cos_angle)
                angles.append(angle)

        # 检查是否有角度在四面体角度附近
        for angle in angles:
            if abs(angle - tetrahedral_angle) < tolerance:
                return False

        # 实用的角度分布标准差要求
        if np.std(angles) < 0.13:  # 实用的随机性要求
            return False

        # 实用的角度范围检查
        min_angle = np.min(angles)
        if min_angle < np.radians(15):  # 最小角度不能小于15°
            return False

        # 检查是否有过于对称的配置
        max_angle = np.max(angles)
        if max_angle > np.radians(165):  # 最大角度不能超过165°
            return False

        return True

    def _check_povm_quality(self, operators, vectors, coefficients):
        """
        检查POVM组的质量 - 进一步优化版本
        确保算符具有极佳的数值性质
        """
        # 实用的正半定性检查
        for op in operators:
            eigenvals = np.linalg.eigvals(op)
            if np.any(eigenvals < -1e-12):  # 实用的数值误差容忍
                return False
            # 检查特征值的数值稳定性
            if np.any(eigenvals > 2.0):  # POVM算符特征值不应过大
                return False

        # 实用的系数平衡性检查
        coeff_ratio = np.max(coefficients) / np.min(coefficients)
        if coeff_ratio > 100:  # 实用的系数比例要求
            return False

        # 基本的系数数值稳定性检查
        if np.any(coefficients < 1e-10) or np.any(coefficients > 50):
            return False

        # 基本的算符条件数检查
        for op in operators:
            if np.linalg.norm(op) > 0:
                try:
                    cond_num = np.linalg.cond(op + 1e-12 * np.eye(2))
                    if cond_num > 1e12:  # 基本的条件数要求
                        return False
                except:
                    pass  # 如果计算失败，跳过此项

        return True

    def _generate_povm_group(self, max_attempts=3000):  # 适度增加尝试次数
        """
        生成一组POVM测量算符 {A, B, C, D}
        满足完备性条件: A + B + C + D = I
        优化参数以提高数值精度
        """
        for attempt in range(max_attempts):
            # 生成前3个随机布洛赫向量
            vectors = []
            for _ in range(3):
                vectors.append(self._generate_random_bloch_vector())

            # 检查角度规律性
            if not self._check_angle_regularity(vectors):
                continue

            # 进一步优化的初始系数 - 使用更精确的分布
            # 使用轻微随机化的初始系数以提高多样性
            base_coeff = 0.45  # 进一步降低基础系数
            c1 = base_coeff + np.random.uniform(-0.05, 0.05)
            c2 = base_coeff + np.random.uniform(-0.05, 0.05)
            c3 = base_coeff + np.random.uniform(-0.05, 0.05)

            # 确保系数为正
            c1, c2, c3 = max(0.1, c1), max(0.1, c2), max(0.1, c3)

            # 计算临时向量
            s_vector = c1 * vectors[0] + c2 * vectors[1] + c3 * vectors[2]
            s_norm = np.linalg.norm(s_vector)

            if s_norm < 1e-12:  # 更严格的除零检查
                continue

            # 第4个向量和系数
            n4 = -s_vector / s_norm
            c4 = s_norm

            vectors.append(n4)
            coefficients = np.array([c1, c2, c3, c4])

            # 更精确的系数调整满足完备性
            c_sum = np.sum(coefficients)
            if c_sum < 1e-12:  # 避免除零
                continue
            coefficients = coefficients * 2.0 / c_sum
            
            # 平衡的系数检查
            if (np.all(coefficients >= 1e-12) and
                np.max(coefficients)/np.min(coefficients) < 50 and  # 适度的平衡性要求
                np.all(coefficients <= 10.0)):  # 适度的上限检查
                # 构造POVM算符
                operators = []
                for i in range(4):
                    c_ik = coefficients[i]
                    n_ik = vectors[i]

                    # E_ik = (c_ik/2) * (I + n⃗_ik · σ⃗)
                    operator = (c_ik / 2) * (
                        self.pauli_matrices['I'] +
                        n_ik[0] * self.pauli_matrices['X'] +
                        n_ik[1] * self.pauli_matrices['Y'] +
                        n_ik[2] * self.pauli_matrices['Z']
                    )
                    operators.append(operator)

                # 验证完备性 - 使用极严格的误差阈值
                sum_operators = sum(operators)
                completeness_error = np.linalg.norm(sum_operators - self.pauli_matrices['I'], 'fro')

                # 适度的多重完备性检查
                trace_error = abs(np.trace(sum_operators) - 2.0)  # 迹应该为2
                max_element_error = np.max(np.abs(sum_operators - self.pauli_matrices['I']))

                if (completeness_error < 1e-11 and  # 适度提高Frobenius范数要求
                    trace_error < 1e-11 and          # 迹误差检查
                    max_element_error < 1e-11):      # 最大元素误差检查
                    # 额外的质量检查
                    if self._check_povm_quality(operators, vectors, coefficients):
                        return {
                            'operators': operators,
                            'vectors': vectors,
                            'coefficients': coefficients,
                            'completeness_error': completeness_error
                        }
        
        raise RuntimeError(f"无法在 {max_attempts} 次尝试内生成有效的POVM组")

    def generate_40_povm_groups(self, max_regeneration_attempts=3):
        """
        生成40组随机动态POVM测量基
        包含智能重新生成机制以确保最高质量
        """
        print("开始生成40组随机POVM测量基...")

        for regeneration_attempt in range(max_regeneration_attempts):
            print(f"\n=== 生成尝试 {regeneration_attempt + 1}/{max_regeneration_attempts} ===")

            self.povm_groups = []
            self.measurement_operators = []

            for i in range(40):
                print(f"生成第 {i+1} 组POVM...", end=' ')

                povm_group = self._generate_povm_group()
                self.povm_groups.append(povm_group)

                # 存储算符用于后续计算
                for j, operator in enumerate(povm_group['operators']):
                    self.measurement_operators.append({
                        'group': i,
                        'index': j,
                        'operator': operator,
                        'vector': povm_group['vectors'][j],
                        'coefficient': povm_group['coefficients'][j]
                    })

                print(f"完成 (完备性误差: {povm_group['completeness_error']:.2e})")

            print(f"\n成功生成40组POVM测量基，共 {len(self.measurement_operators)} 个算符")

            # 验证信息完备性
            if self._verify_information_completeness():
                print("✓ 信息完备性验证通过，POVM组质量优秀")
                return
            else:
                print("⚠ 信息完备性不足，准备重新生成...")
                if regeneration_attempt < max_regeneration_attempts - 1:
                    print("正在重新生成更高质量的POVM组...")

        print("⚠ 警告：经过多次尝试仍未达到最优信息完备性标准，但仍可继续使用")

    def _verify_information_completeness(self):
        """
        验证160个算符的信息完备性
        通过SVD检查测量矩阵的秩
        """
        print("\n验证信息完备性...")

        # 构造测量矩阵 M (160×4)
        # 每行为 (a_0, a_x, a_y, a_z)，其中 E = a_0*I + a_x*σ_x + a_y*σ_y + a_z*σ_z
        measurement_matrix = []

        for op_info in self.measurement_operators:
            operator = op_info['operator']
            c_ik = op_info['coefficient']
            n_ik = op_info['vector']

            # 分解系数
            a_0 = c_ik / 2
            a_x = c_ik * n_ik[0] / 2
            a_y = c_ik * n_ik[1] / 2
            a_z = c_ik * n_ik[2] / 2

            measurement_matrix.append([a_0, a_x, a_y, a_z])

        measurement_matrix = np.array(measurement_matrix)

        # SVD分解
        if scipy_available:
            _, sigma, _ = svd(measurement_matrix)
        else:
            # 使用numpy的SVD作为备用
            _, sigma, _ = np.linalg.svd(measurement_matrix)

        print(f"测量矩阵形状: {measurement_matrix.shape}")
        print(f"奇异值: {sigma}")
        print(f"最小奇异值: {sigma[-1]:.6e}")
        print(f"条件数: {sigma[0]/sigma[-1]:.2e}")

        # 检查秩
        rank = np.sum(sigma > 1e-10)
        print(f"矩阵秩: {rank}")

        # 平衡的信息完备性标准
        min_singular_value_threshold = 2e-2  # 适度提高最小奇异值要求
        condition_number_threshold = 100     # 适度的条件数要求

        condition_number = sigma[0] / sigma[-1] if sigma[-1] > 0 else float('inf')

        if (rank >= 4 and
            sigma[-1] > min_singular_value_threshold and
            condition_number < condition_number_threshold):
            print("✓ 信息完备性验证通过")
            print(f"  最小奇异值: {sigma[-1]:.6f} (要求: >{min_singular_value_threshold})")
            print(f"  条件数: {condition_number:.2f} (要求: <{condition_number_threshold})")
        else:
            print("⚠ 信息完备性不足，建议重新生成POVM组")
            print(f"  最小奇异值: {sigma[-1]:.6f} (要求: >{min_singular_value_threshold})")
            print(f"  条件数: {condition_number:.2f} (要求: <{condition_number_threshold})")

        return (sigma[-1] > min_singular_value_threshold and
                condition_number < condition_number_threshold)

    def calculate_measurement_probabilities(self, density_matrix=None):
        """
        计算所有POVM算符的测量概率
        p_ik = Tr(ρ * E_ik)
        """
        if density_matrix is None:
            density_matrix = self.target_density_matrix

        probabilities = []

        for op_info in self.measurement_operators:
            operator = op_info['operator']
            # 计算迹 Tr(ρ * E_ik)
            prob = np.real(np.trace(density_matrix @ operator))
            probabilities.append({
                'group': op_info['group'],
                'index': op_info['index'],
                'probability': prob,
                'operator': operator
            })

        return probabilities



    def reconstruct_density_matrix(self, measurement_probabilities=None, method='mle'):
        """
        使用最大似然估计重构密度矩阵
        """
        if measurement_probabilities is None:
            measurement_probabilities = self.calculate_measurement_probabilities()

        print(f"\n开始密度矩阵重构 (方法: {method})...")

        # 提取概率数据
        probs_exp = np.array([p['probability'] for p in measurement_probabilities])

        # 参数化密度矩阵: ρ = 1/2 * (I + s⃗ · σ⃗)
        # 优化参数 s = [sx, sy, sz]
        def objective_function(s):
            """目标函数：最小化理论概率与实验概率的差"""
            sx, sy, sz = s

            # 构造密度矩阵
            rho_recon = 0.5 * (
                self.pauli_matrices['I'] +
                sx * self.pauli_matrices['X'] +
                sy * self.pauli_matrices['Y'] +
                sz * self.pauli_matrices['Z']
            )

            # 计算理论概率
            probs_theory = []
            for op_info in self.measurement_operators:
                prob = np.real(np.trace(rho_recon @ op_info['operator']))
                probs_theory.append(prob)

            probs_theory = np.array(probs_theory)

            # 计算误差
            error = np.sum((probs_exp - probs_theory) ** 2)
            return error

        # 约束条件：|s| ≤ 1 (确保有效的量子态)
        def constraint(s):
            return 1.0 - np.linalg.norm(s)

        # 初始猜测
        s0 = np.array([0.0, 0.0, 0.0])

        # 极致优化 - 使用最严格的收敛条件
        if scipy_available:
            # 尝试多种优化方法以获得最佳结果
            best_result = None
            best_error = float('inf')

            # 方法1: SLSQP with ultra-high precision
            try:
                result1 = minimize(
                    objective_function,
                    s0,
                    method='SLSQP',
                    constraints={'type': 'ineq', 'fun': constraint},
                    options={
                        'ftol': 1e-18,      # 极致的函数容忍度
                        'disp': False,
                        'maxiter': 2000,    # 大幅增加最大迭代次数
                        'eps': 1e-12        # 极小的步长用于数值梯度
                    }
                )
                if result1.success and result1.fun < best_error:
                    best_result = result1
                    best_error = result1.fun
            except:
                pass

            # 方法2: L-BFGS-B with high precision
            try:
                bounds = [(-1, 1), (-1, 1), (-1, 1)]  # 布洛赫向量约束
                result2 = minimize(
                    objective_function,
                    s0,
                    method='L-BFGS-B',
                    bounds=bounds,
                    options={
                        'ftol': 1e-18,
                        'gtol': 1e-18,
                        'maxiter': 2000
                    }
                )
                if result2.success and result2.fun < best_error:
                    best_result = result2
                    best_error = result2.fun
            except:
                pass

            # 使用最佳结果
            result = best_result if best_result is not None else result1
        else:
            # 简单的梯度下降备用方法
            result = self._simple_gradient_descent(objective_function, s0, constraint)

        if result.success:
            s_optimal = result.x
            print(f"优化成功！")
            print(f"重构的布洛赫向量: {s_optimal}")
            print(f"目标布洛赫向量: {self.target_bloch_vector}")
            print(f"误差: {np.linalg.norm(s_optimal - self.target_bloch_vector):.6f}")

            # 构造重构的密度矩阵
            rho_reconstructed = 0.5 * (
                self.pauli_matrices['I'] +
                s_optimal[0] * self.pauli_matrices['X'] +
                s_optimal[1] * self.pauli_matrices['Y'] +
                s_optimal[2] * self.pauli_matrices['Z']
            )

            return rho_reconstructed, s_optimal
        else:
            print(f"优化失败: {result.message}")
            return None, None

    def calculate_fidelity(self, rho_reconstructed):
        """
        计算重构密度矩阵与目标密度矩阵的保真度
        F = Tr(√(√ρ_target * ρ_recon * √ρ_target))
        """
        if rho_reconstructed is None:
            return 0.0

        # 对于单量子比特的精确保真度计算
        sqrt_target = self._matrix_sqrt(self.target_density_matrix)
        temp = sqrt_target @ rho_reconstructed @ sqrt_target
        sqrt_temp = self._matrix_sqrt(temp)
        fidelity = np.real(np.trace(sqrt_temp))

        return fidelity

    def _matrix_sqrt(self, matrix):
        """计算矩阵的平方根"""
        eigenvals, eigenvecs = np.linalg.eigh(matrix)
        sqrt_eigenvals = np.sqrt(np.maximum(eigenvals, 0))  # 确保非负
        return eigenvecs @ np.diag(sqrt_eigenvals) @ eigenvecs.conj().T

    def _simple_gradient_descent(self, objective_function, s0, constraint,
                                learning_rate=0.0005, max_iterations=5000, tolerance=1e-15):
        """
        简单的梯度下降优化方法（当scipy不可用时的备用方案）
        """
        s = s0.copy()

        class SimpleResult:
            def __init__(self):
                self.success = False
                self.x = None
                self.message = ""

        result = SimpleResult()

        for iteration in range(max_iterations):
            # 计算数值梯度
            grad = np.zeros_like(s)
            f0 = objective_function(s)

            # 使用中心差分法计算更精确的梯度
            eps = 1e-12  # 极小的步长
            for i in range(len(s)):
                s_plus = s.copy()
                s_minus = s.copy()
                s_plus[i] += eps
                s_minus[i] -= eps
                grad[i] = (objective_function(s_plus) - objective_function(s_minus)) / (2 * eps)

            # 梯度下降步骤
            s_new = s - learning_rate * grad

            # 应用约束：确保 |s| ≤ 1
            s_norm = np.linalg.norm(s_new)
            if s_norm > 1.0:
                s_new = s_new / s_norm

            # 检查收敛
            if np.linalg.norm(s_new - s) < tolerance:
                result.success = True
                result.x = s_new
                result.message = f"收敛于第 {iteration} 次迭代"
                break

            s = s_new

        if not result.success:
            result.x = s
            result.message = f"达到最大迭代次数 {max_iterations}"
            result.success = True  # 仍然返回结果

        return result

    def run_complete_tomography(self, add_noise=False, noise_level=0.01):
        """
        运行完整的量子态层析实验
        """
        print("="*60)
        print("开始完整的量子态层析实验")
        print("="*60)

        # 步骤1：生成40组POVM测量基
        self.generate_40_povm_groups()

        # 步骤2：计算理论测量概率
        print("\n计算理论测量概率...")
        probabilities = self.calculate_measurement_probabilities()

        # 步骤3：添加噪声模拟实验误差（可选）
        if add_noise:
            print(f"添加高斯噪声 (σ = {noise_level})...")
            for prob_info in probabilities:
                noise = np.random.normal(0, noise_level)
                prob_info['probability'] = max(0, min(1, prob_info['probability'] + noise))

        # 步骤4：重构密度矩阵
        rho_reconstructed, bloch_reconstructed = self.reconstruct_density_matrix(probabilities)

        if rho_reconstructed is not None:
            # 步骤5：计算保真度
            fidelity = self.calculate_fidelity(rho_reconstructed)

            # 步骤6：结果分析
            self._analyze_results(rho_reconstructed, bloch_reconstructed, fidelity)

            return {
                'target_density_matrix': self.target_density_matrix,
                'reconstructed_density_matrix': rho_reconstructed,
                'target_bloch_vector': self.target_bloch_vector,
                'reconstructed_bloch_vector': bloch_reconstructed,
                'fidelity': fidelity,
                'povm_groups': len(self.povm_groups),
                'total_measurements': len(self.measurement_operators)
            }
        else:
            print("重构失败！")
            return None

    def _analyze_results(self, rho_reconstructed, bloch_reconstructed, fidelity):
        """
        分析和显示实验结果
        """
        print("\n" + "="*60)
        print("实验结果分析")
        print("="*60)

        print("\n目标密度矩阵:")
        print(self.target_density_matrix)

        print("\n重构密度矩阵:")
        print(rho_reconstructed)

        print(f"\n目标布洛赫向量: {self.target_bloch_vector}")
        print(f"重构布洛赫向量: {bloch_reconstructed}")

        # 计算各种误差指标
        bloch_error = np.linalg.norm(self.target_bloch_vector - bloch_reconstructed)
        matrix_error = np.linalg.norm(self.target_density_matrix - rho_reconstructed, 'fro')

        print(f"\n性能指标:")
        print(f"保真度: {fidelity:.6f}")
        print(f"布洛赫向量误差: {bloch_error:.6f}")
        print(f"密度矩阵Frobenius误差: {matrix_error:.6f}")

        # 验证重构密度矩阵的物理性质
        eigenvals = np.linalg.eigvals(rho_reconstructed)
        trace = np.trace(rho_reconstructed)

        print(f"\n重构密度矩阵验证:")
        print(f"特征值: {eigenvals}")
        print(f"迹: {np.real(trace):.6f}")
        print(f"正半定性: {'✓' if np.all(eigenvals >= -1e-10) else '✗'}")
        print(f"迹为1: {'✓' if abs(np.real(trace) - 1.0) < 1e-6 else '✗'}")

        return {
            'fidelity': fidelity,
            'bloch_error': bloch_error,
            'matrix_error': matrix_error,
            'eigenvalues': eigenvals,
            'trace': trace
        }

    def print_povm_statistics(self):
        """
        打印POVM测量基的统计信息
        """
        if not self.povm_groups:
            print("请先生成POVM测量基")
            return

        # 收集所有布洛赫向量
        all_vectors = []
        for group in self.povm_groups:
            all_vectors.extend(group['vectors'])

        all_vectors = np.array(all_vectors)

        # 统计分析
        print(f"\nPOVM分布统计:")
        print(f"总向量数: {len(all_vectors)}")
        print(f"X坐标范围: [{all_vectors[:, 0].min():.3f}, {all_vectors[:, 0].max():.3f}]")
        print(f"Y坐标范围: [{all_vectors[:, 1].min():.3f}, {all_vectors[:, 1].max():.3f}]")
        print(f"Z坐标范围: [{all_vectors[:, 2].min():.3f}, {all_vectors[:, 2].max():.3f}]")

        # 计算向量间的平均距离
        distances = []
        for i in range(len(all_vectors)):
            for j in range(i+1, len(all_vectors)):
                dist = np.linalg.norm(all_vectors[i] - all_vectors[j])
                distances.append(dist)

        print(f"向量间平均距离: {np.mean(distances):.3f}")
        print(f"向量间距离标准差: {np.std(distances):.3f}")


def main():
    """
    主函数：演示完整的量子态层析实验
    """
    print("量子态层析实验演示")
    print("目标：重构密度矩阵 ρ = 1/2*(I + 0.36σ_x + 0.48σ_y + 0.8σ_z)")

    # 创建实验实例
    qst = QuantumStateTomography()

    # 运行完整实验
    results = qst.run_complete_tomography(add_noise=True, noise_level=0.005)

    if results:
        print(f"\n实验成功完成！")
        print(f"保真度: {results['fidelity']:.6f}")

        # 打印POVM统计信息
        qst.print_povm_statistics()

    return results


if __name__ == "__main__":
    # 设置随机种子以获得可重复的结果
    np.random.seed(42)

    # 运行主程序
    results = main()
